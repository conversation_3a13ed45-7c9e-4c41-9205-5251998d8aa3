import { CircleX } from "lucide-react";
import { cn } from "~/lib/utils";

export const Tag = ({
  label,
  onClose,
  className,
}: {
  label: string;
  onClose?: () => void;
  className?: string;
}) => {
  return (
    <div className={cn("tag border-orange text-orange", className)}>
      <span>{label.replaceAll("_", " ")}</span>

      {onClose && (
        <CircleX
          onClick={onClose}
          className="h-4 w-4 shrink-0 cursor-pointer text-white"
        />
      )}
    </div>
  );
};
