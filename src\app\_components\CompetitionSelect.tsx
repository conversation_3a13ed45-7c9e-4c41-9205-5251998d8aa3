import type { Sport } from "~/lib/enums";
import type { Competition } from "~/lib/formSchemas";
import {
  Command,
  CommandEmpty,
  CommandItem,
  CommandList,
} from "~/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { CheckIcon, ChevronsUpDown } from "lucide-react";
import { api } from "~/trpc/react";
import { cn } from "~/lib/utils";
import { useState } from "react";
import { Input } from "~/components/ui/input";
import { Loading } from "./Loading";
import { Label } from "~/components/ui/label";
import { PopoverClose } from "@radix-ui/react-popover";

export const CompetitionSelect = ({
  sport,
  selectedCompetition,
  setSelectedCompetition,
  label,
  disabled,
}: {
  sport?: Sport | null;
  selectedCompetition?: Competition;
  setSelectedCompetition: (value: Competition) => void;
  label?: string;
  disabled?: boolean;
}) => {
  const [searchText, setSearchText] = useState("");

  const { data: competitions, isFetching } = api.pta.getCompetitions.useQuery(
    { sport: sport! },
    { enabled: !!sport, retry: 5 },
  );

  const filteredCompetitions =
    competitions instanceof Array
      ? competitions?.filter(
          (x) =>
            searchText.length === 0 ||
            x.competition_name
              .toLocaleLowerCase()
              .includes(searchText.toLowerCase()) ||
            (!!x.location &&
              x.location
                .toLocaleLowerCase()
                .includes(searchText.toLowerCase())) ||
            (!!x.start_date &&
              x.start_date
                .toLocaleLowerCase()
                .includes(searchText.toLowerCase())),
        )
      : [];

  return (
    <div className="grid gap-3">
      <Label>{label ?? "Competition"}</Label>
      <Popover>
        <PopoverTrigger asChild disabled={isFetching || !sport || disabled}>
          <div
            className={cn(
              "flex w-full items-center justify-between truncate rounded-none border-b border-primary px-2 py-2",
              !disabled && "cursor-pointer",
              disabled && "cursor-not-allowed opacity-50",
            )}
          >
            <p>
              {selectedCompetition
                ? competitions?.find(
                    (x) => x.competition_id === selectedCompetition.id,
                  )?.competition_name
                : "Select competition..."}
            </p>
            {isFetching && <Loading />}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-full max-w-96 p-0">
          <Command>
            <Input
              placeholder="Search competition..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
            <CommandEmpty>No competition found.</CommandEmpty>
            <CommandList>
              {filteredCompetitions?.map((competition) => (
                <PopoverClose
                  key={competition.competition_id}
                  className="w-full"
                >
                  <CommandItem
                    value={competition.competition_id}
                    onSelect={(currentValue) => {
                      const target = filteredCompetitions.find(
                        (x) => x.competition_id === currentValue,
                      );
                      if (target) {
                        let selectedSport = target.sport.toLowerCase();
                        if (selectedSport === "snow sports") {
                          selectedSport = "snow_sports";
                        }
                        if (selectedSport === "canoe slalom") {
                          selectedSport = "canoe_slalom";
                        }
                        setSelectedCompetition({
                          id: target.competition_id,
                          name: target.competition_name,
                          type: target.competition_type,
                          date: target.start_date,
                          location: target.location,
                          sport: selectedSport as Sport,
                          isOfficial: target.is_official,
                        });
                      }
                    }}
                  >
                    <CheckIcon
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedCompetition &&
                          selectedCompetition.id === competition.competition_id
                          ? "opacity-100"
                          : "opacity-0",
                      )}
                    />
                    <div className="w-full text-left">
                      <p className="font-bold">
                        {competition.competition_name}
                      </p>
                      <p>Location: {competition.location}</p>
                      <p>Date: {competition.start_date}</p>
                    </div>
                  </CommandItem>
                </PopoverClose>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};
