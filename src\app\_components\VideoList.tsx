"use client";

import Image from "next/image";
import Link from "next/link";
import type { VideoAllOutput } from "~/server/api/routers/video";
import { Tag } from "./Tag";
import { EllipsesEdit } from "./EllipsesEdit";
import { Filters } from "./Filters";
import { useState } from "react";

const VideoAthletes = ({
  athletes,
}: {
  athletes: VideoAllOutput["videoList"][number]["athletes"];
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div>
      {athletes && athletes.length > 0 && (
        <div className="flex flex-wrap gap-1">
          <p className="font-bold">Athlete:&nbsp;</p>
          {isExpanded
            ? athletes.map((athlete) => athlete.name).join(", ")
            : `${athletes
                .slice(0, 3)
                .map((athlete) => athlete.name)
                .join(", ")}${athletes.length > 3 ? "..." : ""}`}
          {athletes.length > 3 && (
            <button
              onClick={toggleExpand}
              className="text-orange hover:text-orange/80"
            >
              {isExpanded ? "Show Less" : "Show More"}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export const VideoList = ({
  videos,
}: {
  videos: VideoAllOutput["videoList"];
}) => {
  return (
    <div className="flex flex-col gap-4">
      {videos.length === 0 && (
        <div className="flex items-center justify-center">
          <p className="italic">No videos found</p>
        </div>
      )}
      <div className="block md:hidden">
        <Filters />
      </div>
      <div className="flex flex-col gap-2 md:grid md:grid-cols-3 md:p-4 xl:grid-cols-4">
        {videos.map((video) => (
          <div key={video.id} className="">
            <div className="flex flex-col gap-1 pb-6 text-sm md:p-4">
              <Link
                key={video.id}
                className="flex aspect-video flex-col gap-4 md:w-full md:flex-row"
                href={`/videos/${video.id}`}
              >
                <div className="relative h-full w-full">
                  <Image
                    src={video.thumbnail!}
                    alt="thumbnail"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="rounded-3xl object-cover"
                  />
                  {/* Linked video count badge */}
                  {video.linkedVideoCount && video.linkedVideoCount > 0 && (
                    <div className="absolute bottom-2 right-2 rounded-md bg-black/70 px-2 py-1 text-xs font-medium text-white ">
                      {video.linkedVideoCount} linked
                    </div>
                  )}
                </div>
              </Link>
              <div className="flex items-center justify-between gap-2">
                <p className="flex-1 truncate font-bold capitalize">
                  {video.title}
                </p>
                {/* {(!!userSession?.data?.user.roles.includes(UserRole.admin) ||
                  video.createdById === userSession.data?.user.id ||
                  !!userSession.data?.user.roles.includes(
                    UserRole.analyst,
                  )) && <EllipsesEdit video={video} />} */}
                <EllipsesEdit video={video} />
              </div>
              <div className="flex">
                <p className="font-bold">Status:&nbsp;</p>
                <p>{video.status?.replaceAll("_", " ")}</p>
              </div>
              {video.athletes.length > 0 && (
                <VideoAthletes athletes={video.athletes} />
              )}
              {video.competition && (
                <div>
                  <div className="flex gap-3 font-bold">
                    <p>Competition: {video.competition.date}</p>
                  </div>
                  <p>{video.competition?.name}</p>
                  <p>{video.competition?.location}</p>
                </div>
              )}
              <div className="flex flex-row flex-wrap gap-2">
                {video.sport && <Tag label={video.sport} />}
                {video.tags.map((tag) => (
                  <div key={tag.text} className="font-bold">
                    <Tag label={tag.text} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
