{"openapi": "3.0.0", "info": {"title": "Swagger API DOC", "version": "1.0"}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "paths": {"/api/v1/video/{id}/athletes": {"put": {"description": "Update video athletes.\n\n**Roles required:** api, api_modify, admin / analyst\n", "tags": ["Update video"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["athletes"], "properties": {"athletes": {"type": "array", "items": {"type": "object", "properties": {"athleteId": {"type": "string"}}}}}}}}}, "responses": {"200": {"description": "Video summary", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"athleteId": {"type": "string"}, "isHp": {"type": "boolean"}, "name": {"type": "string"}}}}}}}}}}, "/api/v1/video/{id}/convert": {"post": {"description": "To trigger video conversion. It happens automatically on upload, but can be triggered manually.\n\n**Roles required:** api, admin / analyst\n", "tags": ["Upload"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"forceReplace": {"type": "boolean", "default": false}, "createSourceStream": {"type": "boolean"}, "encodeAdaptiveStreams": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Video summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/api/v1/video/{id}/playbackUrl": {"get": {"description": "Get video playback url.", "tags": ["Video playback"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Video source url", "content": {"application/json": {"schema": {"type": "object", "properties": {"videoUrl": {"type": "string"}, "thumbnailUrl": {"type": "string"}}}}}}}}}, "/api/v1/video/{id}": {"get": {"description": "Get a video summary.", "tags": ["Get/list video(s)"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Summary of a video", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "thumbnail": {"type": "string"}, "sport": {"type": "string", "enum": ["cycling", "kayak", "rowing", "sailing", "snow_sports", "canoe_slalom", "swimming", "training", "trampoline"]}, "permission": {"type": "string", "enum": ["public", "restricted"]}, "isDraft": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "fps": {"type": "number"}, "snowSportsRaceId": {"type": "string"}, "status": {"type": "string", "enum": ["Raw", "Trimmed", "Tagged_by_AI", "AI_in_Progress", "AI_Failed", "Tagged", "Review", "Analysed"]}, "competitionId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}}}}}}}, "put": {"description": "Update a video.\n\n**Roles required:** api, api_modify, admin / analyst\n", "tags": ["Update video"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"filename": {"type": "string"}, "title": {"type": "string"}, "thumbnailTime": {"type": "number"}, "sport": {"type": "string", "enum": ["cycling", "kayak", "rowing", "sailing", "snow_sports", "canoe_slalom", "swimming", "training", "trampoline"]}, "permission": {"type": "string", "enum": ["public", "restricted"]}, "isDraft": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "status": {"type": "string", "enum": ["Raw", "Trimmed", "Tagged_by_AI", "AI_in_Progress", "AI_Failed", "Tagged", "Review", "Analysed"]}, "competitionId": {"type": "string"}, "fps": {"type": "number"}, "videoDate": {"type": "string"}}}}}}, "responses": {"200": {"description": "Video summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "thumbnail": {"type": "string"}, "sport": {"type": "string"}, "permission": {"type": "string"}, "path": {"type": "string"}, "isDraft": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "status": {"type": "string"}, "fps": {"type": "number"}, "competitionId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}}}}}}}}, "/api/v1/video/{id}/sourceStream": {"get": {"description": "Get video stream optimised for seeking.", "tags": ["Video playback"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "The video source stream file (m3u8) with signed URLs for each segment OR a faststart enabled MP4, depending on original video format.", "content": {"application/x-mpegURL": {"schema": {"type": "string", "format": "binary"}}, "application/json": {"schema": {"type": "object", "properties": {"videoUrl": {"type": "string"}, "cacheUrl": {"type": "string"}, "expiry": {"type": "string"}}}}}}}}}, "/api/v1/video/{id}/sourceUrl": {"get": {"description": "Get video source url.", "tags": ["Video playback"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Video source url", "content": {"application/json": {"schema": {"type": "object", "properties": {"videoUrl": {"type": "string"}, "cacheUrl": {"type": "string"}, "expiry": {"type": "string"}}}}}}}}}, "/api/v1/video/{id}/status": {"put": {"description": "Update a video status.\n\n**Roles required:** api, api_modify, admin / analyst\n", "tags": ["Update video"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["Raw", "Trimmed", "Tagged_by_AI", "Tagged", "Review", "Analysed"]}}}}}}, "responses": {"200": {"description": "Video summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "sport": {"type": "string"}, "permission": {"type": "string"}, "isDraft": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "status": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}}}}}}}}, "/api/v1/video/{id}/tags": {"get": {"description": "Get video tags.", "tags": ["Get/list video(s)"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Video tags", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}}}}}}}}, "put": {"description": "Update tags to a video. Tags must match video sport metadata.\n\n**Roles required:** api, api_modify, admin / analyst\n", "tags": ["Update video"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"add": {"type": "array", "items": {"type": "string"}}, "remove": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "Video tags", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}}}}}}}}}, "/api/v1/video/{id}/upload": {"put": {"description": "Complete upload a video. Call this endpoint after video is uploaded to s3 bucket.\n\n**Roles required:** api, admin / analyst\n", "tags": ["Upload"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "The video ID"}], "responses": {"200": {"description": "Video summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "thumbnail": {"type": "string"}, "sport": {"type": "string"}, "permission": {"type": "string"}, "isDraft": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "status": {"type": "string"}, "competitionId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}}}}}}}}, "/api/v1/video": {"get": {"description": "Get a list of videos summary.", "tags": ["Get/list video(s)"], "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "pageSize", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "The number of items to return, default 10"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 0, "default": 0}, "description": "The page number"}, {"in": "query", "name": "sortBy", "schema": {"type": "string", "enum": ["name", "video_date"]}, "description": "The page number"}, {"in": "query", "name": "sports", "schema": {"type": "string", "enum": ["cycling", "kayak", "rowing", "sailing", "snow_sports", "canoe_slalom", "swimming", "training", "trampoline"]}, "description": "Filter videos by sport (optional)"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["Raw", "Trimmed", "Tagged_by_AI", "Tagged", "Review", "Analysed"]}, "description": "Filter videos by status (optional)"}, {"in": "query", "name": "searchText", "schema": {"type": "string"}, "description": "Filter videos by search (optional)"}], "responses": {"200": {"description": "A list of videos summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"videosCount": {"type": "number"}, "videoList": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "sports": {"type": "string"}, "permission": {"type": "string"}, "path": {"type": "string"}, "status": {"type": "string", "enum": ["Raw", "Trimmed", "Tagged_by_AI", "AI_in_Progress", "AI_Failed", "Tagged", "Review", "Analysed"]}, "duration": {"type": "number"}, "fps": {"type": "number"}, "isDraft": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}}}}}}}}}}}, "/api/v1/video/upload/complete": {"post": {"description": "To complete multipart upload. Call this endpoint after all parts are uploaded.\n\n**Roles required:** api, admin / analyst\n", "tags": ["Upload"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title", "filename", "sport", "permission"], "properties": {"uploadId": {"type": "string"}, "fullFilename": {"type": "string"}, "parts": {"type": "array", "items": {"type": "object", "properties": {"partNumber": {"type": "number"}, "eTag": {"type": "string"}}}}}}}}}, "responses": {"200": {"description": "A list of videos summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"uploadId": {"type": "string"}}}}}}}}}, "/api/v1/video/upload": {"post": {"description": "Get upload url(s) to upload a video. When chunkSize and fileSize are not passed, or chunkSize is larger than file size, it will do normal upload and return an upload url, uploadUrls will be empty. When passing chunkSize and fileSize, it will do multipart upload and return an uploadId and uploadUrls will contain multiple signed urls. You will need this uploadId and ETag, part number returned from part uploads to complete the whole upload. You can get ETag from response headers.\n\n**Roles required:** api, admin / analyst\n", "tags": ["Upload"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title", "filename", "sport", "permission"], "properties": {"title": {"type": "string"}, "filename": {"type": "string"}, "sport": {"type": "string"}, "permission": {"type": "string", "enum": ["public", "restricted"]}, "chunkSize": {"type": "number"}, "fileSize": {"type": "number"}}}}}}, "responses": {"200": {"description": "A list of videos summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"videoId": {"type": "string"}, "uploadUrl": {"type": "string", "format": "uri"}, "uploadId": {"type": "string"}, "fullFilename": {"type": "string"}, "uploadUrls": {"type": "array", "items": {"type": "object", "properties": {"partNumber": {"type": "number"}, "signedUrl": {"type": "string"}}}}, "expiry": {"type": "string"}}}}}}}}}}, "tags": []}